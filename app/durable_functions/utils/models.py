from enum import Str<PERSON><PERSON>
from typing import Annotated

from pydantic import BaseModel, BeforeValidator


def _none_string_validator(value: str | None) -> str | None:
    """Convert string 'None' to actual None value, case-insensitively."""
    if isinstance(value, str) and value.lower() == 'none':
        return None
    return value


NoneStr = Annotated[str | None, BeforeValidator(_none_string_validator)]


class DFBaseModel(BaseModel):
    def to_json(self):
        return self.model_dump_json()

    @classmethod
    def from_json(cls, obj: str):
        return cls.model_validate_json(obj)


class UserConnectionAttributes(BaseModel):
    url: str
    access_token: str


class JwtClaimNames(StrEnum):
    AUD = 'aud'
    SUB = 'sub'
    EXP = 'exp'


class LLMExtractedDataResult(DFBaseModel):
    """
    This model is used to extract the data from the single chunk of text using LLM.
    """

    client_name: NoneStr = None
    ldmf_country: NoneStr = None
    engagement_start_date: NoneStr = None
    engagement_end_date: NoneStr = None
    engagement_start_date_original: NoneStr = None
    engagement_end_date_original: NoneStr = None
    objective_and_scope: NoneStr = None
    outcomes: NoneStr = None
    other: NoneStr = None


class EngagementPeriod(DFBaseModel):
    start_date: str | None = None
    end_date: str | None = None


class FinalExtractionDataResults(DFBaseModel):
    """
    Model to store the final extraction data results. One or many results of chunks extraction.
    """

    client_names: list[str] | None = None
    lead_member_countries: list[str] | None = None
    periods: list[EngagementPeriod] | None = None
    periods_original: list[EngagementPeriod] | None = None
    objective_and_scope: str | None = None
    outcomes: str | None = None

    @property
    def is_complete(self) -> bool:
        """Check if every field is not None and has values (non-empty for lists/strings)."""
        return all(
            [
                self.client_names is not None and len(self.client_names) == 1,
                self.lead_member_countries is not None and len(self.lead_member_countries) == 1,
                self.periods is not None and len(self.periods) == 1,
                self.objective_and_scope is not None and self.objective_and_scope.strip() != '',
                self.outcomes is not None and self.outcomes.strip() != '',
            ]
        )
